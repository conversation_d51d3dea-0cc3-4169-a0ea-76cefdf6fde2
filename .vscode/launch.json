{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "gcc-try",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/dblayer/try",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}/dblayer",
            "environment": [],
            "externalConsole": true,
            "MIMode": "gdb",
            "setupCommands": [
                {
                    "description": "Enable pretty-printing for gdb",
                    "text": "-enable-pretty-printing",
                    "ignoreFailures": true
                }
            ],
            "preLaunchTask": "build-try"
        }
    ]
}